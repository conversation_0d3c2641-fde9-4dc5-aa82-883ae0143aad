# 域名容灾接入方案

## 接入背景

随着业务规模的不断扩大和用户对服务可用性要求的日益提高，公司对业务前端的稳定性和容灾能力提出了更高的标准。为了应对可能出现的域名解析故障、DNS劫持、网络异常等突发情况，确保用户能够在主域名不可用时仍能正常访问业务服务，公司要求所有业务前端必须具备**页面逃生能力**。

页面逃生能力是指当主域名出现故障或不可访问时，系统能够自动或手动切换到备用域名，保障用户的正常访问和业务的连续性。这不仅是技术层面的要求，更是保障用户体验、降低业务风险、维护公司声誉的重要措施。

基于此背景，我们制定了分级的域名容灾接入方案，帮助各业务线根据自身的重要性和资源情况，选择合适的容灾等级，逐步建立完善的域名容灾体系。

## 容灾能力分级接入

| 星级 | 容灾能力 | 适用项目 | 容灾能力描述 |
|------|-------------|-------------|----------|
| 🌟🌟🌟🌟🌟 | IDC 容灾 - 平权配置模式 | P0/P1 核心项目 | **能力范围**：IDC 容灾平权配置模式（原单元调度）<br>**实现方式**：多IDC负载均衡，智能流量调度，无主备概念<br>**业务保障**：提供最高级别的可用性保障 |
| 🌟🌟🌟🌟 | IDC 容灾 - Failover 模式 | 重要项目 | **能力范围**：IDC 级别的 Failover 故障切换<br>**实现方式**：主备IDC架构，故障时自动切换到备用IDC<br>**业务保障**：提供IDC级别的高可用保障 |
| 🌟🌟🌟 | 前端工程页面都支持备用域名访问 | 一般项目 | **能力范围**：前端工程所有页面都支持备用域名访问<br>**实现方式**：全站页面统一配置备用域名，实现完整的前端容灾<br>**业务保障**：提供全面的页面访问保障 |
| 🌟🌟 | 前端页面核心链路支持备用域名访问 | 非重要项目 | **能力范围**：前端页面核心业务链路支持备用域名访问<br>**实现方式**：识别 PX 链路（核心业务页面），为关键路径配置备用域名<br>**业务保障**：保障核心功能正常运行 |
| 🌟 | 部分页面支持备用域名访问 | 基础项目 | **能力范围**：部分关键页面支持备用域名访问<br>**实现方式**：为重要页面配置备用域名，确保基本可访问性<br>**业务保障**：满足最低容灾要求 |


### 项目等级评估标准

| 评估维度 | P0/P1 核心项目 | 重要项目 | 一般项目 | 非重要项目 |
|----------|---------------|----------|----------|------------|
| **流量影响** | 影响核心业务流量 | 影响重要功能流量 | 影响部分功能流量 | 影响较小 |
| **资损风险** | 高资损风险 | 中等资损风险 | 低资损风险 | 基本无资损 |
| **故障等级** | 影响业务连续性 | 影响用户体验 | 功能降级 | 轻微影响 |
| **恢复时间要求** | 秒级/分钟级 | 分钟级 | 小时级 | 天级 |

### 接入原则

1. **分级接入**：根据项目重要性选择对应的容灾等级
2. **渐进式升级**：可从低等级开始，逐步提升容灾能力
3. **成本效益平衡**：在保障业务的前提下，选择最适合的容灾方案
4. **定期评估**：根据业务发展调整项目等级和容灾需求
	

